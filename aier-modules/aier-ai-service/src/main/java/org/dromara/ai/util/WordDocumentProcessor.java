/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.util;

import lombok.extern.slf4j.Slf4j;
import org.dromara.ai.config.DocumentProcessingConfig;
import org.dromara.ai.service.IDocumentCleaningService;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.tika.TikaDocumentReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Word文档处理器
 * 使用Spring AI的TikaDocumentReader处理Word文档
 * 集成智能化文档处理流程：清洗、分割、元数据增强
 *
 * <AUTHOR> Puppy
 */
@Slf4j
@Component
public class WordDocumentProcessor implements DocumentProcessor {

    @Autowired
    private IDocumentCleaningService documentCleaningService;

    @Autowired
    private SmartTextSplitter smartTextSplitter;

    @Autowired
    private DocumentMetadataEnhancer metadataEnhancer;

    @Autowired
    private DocumentProcessingConfig processingConfig;

    @Override
    public boolean supports(String fileType) {
        return "docx".equalsIgnoreCase(fileType) || "doc".equalsIgnoreCase(fileType);
    }

    @Override
    public List<Document> process(InputStream inputStream, Map<String, Object> metadata) throws Exception {
        log.info("开始智能化处理Word文档，元数据: {}", metadata);

        try {
            // 1. 预处理：读取Word文档
            List<Document> rawDocuments = preprocess(inputStream, metadata);

            // 2. 文档清洗
            List<Document> cleanedDocuments = documentCleaningService.cleanDocuments(rawDocuments, "docx");

            // 3. 智能分割
            List<Document> splitDocuments = smartSplit(cleanedDocuments);

            // 4. 元数据增强
            List<Document> enhancedDocuments = enhanceMetadata(splitDocuments, metadata);

            // 5. 质量验证
            List<Document> finalDocuments = validateQuality(enhancedDocuments);

            log.info("Word文档智能化处理完成 - 原始: {}, 清洗后: {}, 分割后: {}, 最终: {}",
                rawDocuments.size(), cleanedDocuments.size(), splitDocuments.size(), finalDocuments.size());

            return finalDocuments;

        } catch (Exception e) {
            log.error("Word文档智能化处理失败", e);
            throw new RuntimeException("Word文档智能化处理失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Document> preprocess(InputStream inputStream, Map<String, Object> metadata) throws Exception {
        log.debug("开始Word文档预处理");

        // 使用Spring AI的TikaDocumentReader处理Word文档
        TikaDocumentReader documentReader = new TikaDocumentReader(new InputStreamResource(inputStream));

        // 读取文档
        List<Document> documents = documentReader.get();

        if (documents.isEmpty()) {
            log.warn("Word文档内容为空");
            return new ArrayList<>();
        }

        // 为每个文档添加基础元数据
        for (int i = 0; i < documents.size(); i++) {
            Document document = documents.get(i);
            Map<String, Object> docMetadata = new HashMap<>(metadata);

            // 添加Word特有的元数据
            docMetadata.put("document_index", i);
            docMetadata.put("total_documents", documents.size());
            docMetadata.put("document_type", "docx");
            docMetadata.put("source_format", "word");

            document.getMetadata().putAll(docMetadata);
        }

        log.debug("Word文档预处理完成，文档数: {}", documents.size());
        return documents;
    }

    @Override
    public List<Document> smartSplit(List<Document> documents) {
        if (documents == null || documents.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("开始Word文档智能分割，文档数: {}", documents.size());

        // 获取Word特定的分块配置
        int chunkSize = processingConfig.getChunkSizeForType("docx");
        double overlapRatio = processingConfig.getOverlapRatioForType("docx");

        // 使用智能分割器，针对Word优化分割策略
        SmartTextSplitter wordSplitter = new SmartTextSplitter(chunkSize,
            processingConfig.getSmartSplitter().getMinChunkSize(),
            processingConfig.getSmartSplitter().getMaxChunkSize(),
            overlapRatio);

        List<Document> splitDocuments = wordSplitter.splitDocuments(documents, "docx");

        log.debug("Word文档智能分割完成，分割后块数: {}", splitDocuments.size());
        return splitDocuments;
    }

    @Override
    public List<Document> enhanceMetadata(List<Document> documents, Map<String, Object> additionalMetadata) {
        if (documents == null || documents.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("开始Word文档元数据增强，文档数: {}", documents.size());

        // 添加Word特有的元数据
        Map<String, Object> wordMetadata = new HashMap<>(additionalMetadata != null ? additionalMetadata : new HashMap<>());
        wordMetadata.put("processor_type", "word_smart");
        wordMetadata.put("processing_strategy", "paragraph_aware_splitting");

        List<Document> enhancedDocuments = metadataEnhancer.enhanceDocumentsMetadata(documents, "docx", wordMetadata);

        log.debug("Word文档元数据增强完成");
        return enhancedDocuments;
    }

    @Override
    public List<Document> validateQuality(List<Document> documents) {
        if (documents == null || documents.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("开始Word文档质量验证，文档数: {}", documents.size());

        List<Document> validDocuments = new ArrayList<>();
        int filteredCount = 0;

        for (Document document : documents) {
            // 检查文档内容质量
            if (documentCleaningService.isValidContent(document.getText())) {
                // 检查质量分数
                Object qualityScore = document.getMetadata().get("overall_quality_score");
                if (qualityScore instanceof Double && (Double) qualityScore >= processingConfig.getQuality().getMinOverallQualityScore()) {
                    validDocuments.add(document);
                } else {
                    filteredCount++;
                    log.debug("过滤低质量Word文档块，质量分数: {}", qualityScore);
                }
            } else {
                filteredCount++;
                log.debug("过滤无效Word文档块，内容长度: {}", document.getText().length());
            }
        }

        log.debug("Word文档质量验证完成，有效文档: {}, 过滤文档: {}", validDocuments.size(), filteredCount);
        return validDocuments;
    }

    @Override
    public List<String> getSupportedFileTypes() {
        return List.of("docx", "doc");
    }

    @Override
    public String getProcessorName() {
        return "智能Word文档处理器";
    }

    @Override
    public String getProcessorVersion() {
        return "2.0.0-smart";
    }

    @Override
    public boolean supportsSmartProcessing() {
        return true;
    }

    @Override
    public String getConfigurationInfo() {
        return String.format("SmartWordProcessor[chunk_size=%d, overlap=%.2f, cleaning=%s, metadata=%s]",
            processingConfig.getChunkSizeForType("docx"),
            processingConfig.getOverlapRatioForType("docx"),
            processingConfig.getCleaning().isEnabled(),
            processingConfig.getMetadata().isEnabled());
    }

}
