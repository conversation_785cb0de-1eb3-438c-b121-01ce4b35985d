package org.dromara.ai.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;

/**
 * 文档处理配置类
 * 统一管理文档处理相关的配置参数
 *
 * <AUTHOR>
 */
@Slf4j
@Data
@Configuration
@ConfigurationProperties(prefix = "spring.ai.document-processing")
public class DocumentProcessingConfig {

    /**
     * 智能分割器配置
     */
    private SmartSplitterConfig smartSplitter = new SmartSplitterConfig();

    /**
     * 文档清洗配置
     */
    private CleaningConfig cleaning = new CleaningConfig();

    /**
     * 元数据增强配置
     */
    private MetadataConfig metadata = new MetadataConfig();

    /**
     * 质量控制配置
     */
    private QualityConfig quality = new QualityConfig();

    /**
     * 针对不同文档类型的特殊配置
     */
    private Map<String, DocumentTypeConfig> documentTypes = new HashMap<>();

    /**
     * 智能分割器配置
     */
    @Data
    public static class SmartSplitterConfig {
        /**
         * 目标块大小（字符数）
         */
        private int targetChunkSize = 600;

        /**
         * 最小块大小（字符数）
         */
        private int minChunkSize = 100;

        /**
         * 最大块大小（字符数）
         */
        private int maxChunkSize = 1200;

        /**
         * 重叠比例（0.0-1.0）
         */
        private double overlapRatio = 0.15;

        /**
         * 最小重叠大小（字符数）
         */
        private int minOverlapSize = 20;

        /**
         * 是否启用语义感知分割
         */
        private boolean enableSemanticSplitting = true;

        /**
         * 是否启用结构化分割（针对Markdown等）
         */
        private boolean enableStructuralSplitting = true;
    }

    /**
     * 文档清洗配置
     */
    @Data
    public static class CleaningConfig {
        /**
         * 是否启用文档清洗
         */
        private boolean enabled = true;

        /**
         * 是否去除页眉页脚
         */
        private boolean removeHeaderFooter = true;

        /**
         * 是否去除重复内容
         */
        private boolean removeDuplicates = true;

        /**
         * 是否格式规范化
         */
        private boolean normalizeFormat = true;

        /**
         * 是否去除无意义字符序列
         */
        private boolean removeMeaninglessSequences = true;

        /**
         * 最小内容质量分数阈值
         */
        private double minQualityScore = 30.0;

        /**
         * 最小有效内容长度
         */
        private int minValidContentLength = 5;
    }

    /**
     * 元数据增强配置
     */
    @Data
    public static class MetadataConfig {
        /**
         * 是否启用元数据增强
         */
        private boolean enabled = true;

        /**
         * 是否提取关键词
         */
        private boolean extractKeywords = true;

        /**
         * 关键词提取数量限制
         */
        private int maxKeywords = 10;

        /**
         * 是否生成摘要
         */
        private boolean generateSummary = true;

        /**
         * 摘要最大长度
         */
        private int maxSummaryLength = 100;

        /**
         * 是否进行语言检测
         */
        private boolean detectLanguage = true;

        /**
         * 是否分析内容类型
         */
        private boolean analyzeContentType = true;

        /**
         * 是否计算质量指标
         */
        private boolean calculateQualityMetrics = true;
    }

    /**
     * 质量控制配置
     */
    @Data
    public static class QualityConfig {
        /**
         * 是否启用质量控制
         */
        private boolean enabled = true;

        /**
         * 最小可读性分数
         */
        private double minReadabilityScore = 30.0;

        /**
         * 最小信息密度
         */
        private double minInformationDensity = 20.0;

        /**
         * 最小完整性分数
         */
        private double minCompletenessScore = 30.0;

        /**
         * 最小综合质量分数
         */
        private double minOverallQualityScore = 40.0;

        /**
         * 是否自动过滤低质量内容
         */
        private boolean autoFilterLowQuality = true;
    }

    /**
     * 文档类型特殊配置
     */
    @Data
    public static class DocumentTypeConfig {
        /**
         * 文档类型名称
         */
        private String typeName;

        /**
         * 特殊的分块大小
         */
        private Integer customChunkSize;

        /**
         * 特殊的重叠比例
         */
        private Double customOverlapRatio;

        /**
         * 特殊的清洗规则
         */
        private boolean enableSpecialCleaning = false;

        /**
         * 特殊的分割策略
         */
        private String splittingStrategy = "default";

        /**
         * 额外的元数据字段
         */
        private Map<String, Object> additionalMetadata = new HashMap<>();
    }

    /**
     * 获取指定文档类型的配置
     */
    public DocumentTypeConfig getDocumentTypeConfig(String documentType) {
        return documentTypes.getOrDefault(documentType.toLowerCase(), new DocumentTypeConfig());
    }

    /**
     * 获取指定文档类型的块大小
     */
    public int getChunkSizeForType(String documentType) {
        DocumentTypeConfig typeConfig = getDocumentTypeConfig(documentType);
        return typeConfig.getCustomChunkSize() != null ? 
               typeConfig.getCustomChunkSize() : 
               smartSplitter.getTargetChunkSize();
    }

    /**
     * 获取指定文档类型的重叠比例
     */
    public double getOverlapRatioForType(String documentType) {
        DocumentTypeConfig typeConfig = getDocumentTypeConfig(documentType);
        return typeConfig.getCustomOverlapRatio() != null ? 
               typeConfig.getCustomOverlapRatio() : 
               smartSplitter.getOverlapRatio();
    }

    /**
     * 检查是否启用智能处理
     */
    public boolean isSmartProcessingEnabled() {
        return smartSplitter.isEnableSemanticSplitting() || 
               smartSplitter.isEnableStructuralSplitting() ||
               cleaning.isEnabled() ||
               metadata.isEnabled();
    }

    /**
     * 获取处理配置摘要
     */
    public String getConfigSummary() {
        return String.format(
            "DocumentProcessing[chunk:%d-%d, overlap:%.2f, cleaning:%s, metadata:%s, quality:%s]",
            smartSplitter.getMinChunkSize(),
            smartSplitter.getMaxChunkSize(),
            smartSplitter.getOverlapRatio(),
            cleaning.isEnabled(),
            metadata.isEnabled(),
            quality.isEnabled()
        );
    }

    /**
     * 初始化默认的文档类型配置
     * 在Spring容器启动后自动调用
     */
    @PostConstruct
    public void initializeDefaultConfigs() {
        if (documentTypes.isEmpty()) {
            // PDF配置
            DocumentTypeConfig pdfConfig = new DocumentTypeConfig();
            pdfConfig.setTypeName("pdf");
            pdfConfig.setCustomChunkSize(800);  // PDF通常内容密度较高
            pdfConfig.setEnableSpecialCleaning(true);
            pdfConfig.setSplittingStrategy("page-aware");
            documentTypes.put("pdf", pdfConfig);

            // Word配置
            DocumentTypeConfig wordConfig = new DocumentTypeConfig();
            wordConfig.setTypeName("word");
            wordConfig.setCustomChunkSize(700);
            wordConfig.setEnableSpecialCleaning(true);
            wordConfig.setSplittingStrategy("paragraph-aware");
            documentTypes.put("docx", wordConfig);
            documentTypes.put("doc", wordConfig);

            // Markdown配置
            DocumentTypeConfig markdownConfig = new DocumentTypeConfig();
            markdownConfig.setTypeName("markdown");
            markdownConfig.setCustomChunkSize(600);
            markdownConfig.setSplittingStrategy("heading-aware");
            documentTypes.put("md", markdownConfig);
            documentTypes.put("markdown", markdownConfig);

            // 文本配置
            DocumentTypeConfig textConfig = new DocumentTypeConfig();
            textConfig.setTypeName("text");
            textConfig.setCustomChunkSize(500);
            textConfig.setSplittingStrategy("sentence-aware");
            documentTypes.put("txt", textConfig);

            log.info("文档处理配置初始化完成: {}", getConfigSummary());
        }
    }
}
