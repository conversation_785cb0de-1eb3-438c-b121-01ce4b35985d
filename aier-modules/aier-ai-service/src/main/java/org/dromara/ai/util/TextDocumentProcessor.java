/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.dromara.ai.config.DocumentProcessingConfig;
import org.dromara.ai.service.IDocumentCleaningService;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.TextReader;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 文本文档处理器
 * 处理纯文本文件和Markdown文件
 * 集成智能化文档处理流程：清洗、分割、元数据增强
 *
 * <AUTHOR> Puppy
 */
@Slf4j
@Component
public class TextDocumentProcessor implements DocumentProcessor {

    @Autowired
    private IDocumentCleaningService documentCleaningService;

    @Autowired
    private SmartTextSplitter smartTextSplitter;

    @Autowired
    private DocumentMetadataEnhancer metadataEnhancer;

    @Autowired
    private DocumentProcessingConfig processingConfig;

    @Override
    public boolean supports(String fileType) {
        return "txt".equalsIgnoreCase(fileType) || 
               "md".equalsIgnoreCase(fileType) || 
               "markdown".equalsIgnoreCase(fileType);
    }

    @Override
    public List<Document> process(InputStream inputStream, Map<String, Object> metadata) throws Exception {
        log.info("开始智能化处理文本文档，元数据: {}", metadata);

        try {
            // 1. 预处理：读取文本文档
            List<Document> rawDocuments = preprocess(inputStream, metadata);

            // 2. 文档清洗
            String documentType = determineDocumentType(metadata);
            List<Document> cleanedDocuments = documentCleaningService.cleanDocuments(rawDocuments, documentType);

            // 3. 智能分割
            List<Document> splitDocuments = smartSplit(cleanedDocuments);

            // 4. 元数据增强
            List<Document> enhancedDocuments = enhanceMetadata(splitDocuments, metadata);

            // 5. 质量验证
            List<Document> finalDocuments = validateQuality(enhancedDocuments);

            log.info("文本文档智能化处理完成 - 原始: {}, 清洗后: {}, 分割后: {}, 最终: {}",
                rawDocuments.size(), cleanedDocuments.size(), splitDocuments.size(), finalDocuments.size());

            return finalDocuments;

        } catch (Exception e) {
            log.error("文本文档智能化处理失败", e);
            throw new RuntimeException("文本文档智能化处理失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<Document> preprocess(InputStream inputStream, Map<String, Object> metadata) throws Exception {
        log.debug("开始文本文档预处理");

        // 使用Spring AI的TextReader读取文本
        TextReader textReader = new TextReader(new InputStreamResource(inputStream));
        textReader.getCustomMetadata().putAll(metadata);

        // 读取文档
        List<Document> documents = textReader.get();

        if (documents.isEmpty()) {
            log.warn("文本文档内容为空");
            return new ArrayList<>();
        }

        // 确定文档类型
        String documentType = determineDocumentType(metadata);

        // 验证和增强文档内容
        List<Document> validDocuments = new ArrayList<>();
        for (int i = 0; i < documents.size(); i++) {
            Document document = documents.get(i);
            if (StrUtil.isNotBlank(document.getText())) {
                Map<String, Object> docMetadata = new HashMap<>(metadata);

                // 添加文本特有的元数据
                docMetadata.put("document_index", i);
                docMetadata.put("total_documents", documents.size());
                docMetadata.put("document_type", documentType);
                docMetadata.put("source_format", "text");

                document.getMetadata().putAll(docMetadata);
                validDocuments.add(document);
            } else {
                log.warn("发现空白文档内容，已跳过");
            }
        }

        log.debug("文本文档预处理完成，有效文档数: {}", validDocuments.size());
        return validDocuments;
    }

    @Override
    public List<Document> smartSplit(List<Document> documents) {
        if (documents == null || documents.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("开始文本文档智能分割，文档数: {}", documents.size());

        // 根据文档类型确定分割策略
        String documentType = (String) documents.get(0).getMetadata().get("document_type");
        if (documentType == null) {
            documentType = "txt";
        }

        // 获取文本特定的分块配置
        int chunkSize = processingConfig.getChunkSizeForType(documentType);
        double overlapRatio = processingConfig.getOverlapRatioForType(documentType);

        // 使用智能分割器，针对文本类型优化分割策略
        SmartTextSplitter textSplitter = new SmartTextSplitter(chunkSize,
            processingConfig.getSmartSplitter().getMinChunkSize(),
            processingConfig.getSmartSplitter().getMaxChunkSize(),
            overlapRatio);

        List<Document> splitDocuments = textSplitter.splitDocuments(documents, documentType);

        log.debug("文本文档智能分割完成，分割后块数: {}", splitDocuments.size());
        return splitDocuments;
    }

    @Override
    public List<Document> enhanceMetadata(List<Document> documents, Map<String, Object> additionalMetadata) {
        if (documents == null || documents.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("开始文本文档元数据增强，文档数: {}", documents.size());

        // 确定文档类型
        String documentType = (String) documents.get(0).getMetadata().get("document_type");
        if (documentType == null) {
            documentType = "txt";
        }

        // 添加文本特有的元数据
        Map<String, Object> textMetadata = new HashMap<>(additionalMetadata != null ? additionalMetadata : new HashMap<>());
        textMetadata.put("processor_type", "text_smart");

        // 根据文档类型设置处理策略
        if ("md".equals(documentType) || "markdown".equals(documentType)) {
            textMetadata.put("processing_strategy", "heading_aware_splitting");
        } else {
            textMetadata.put("processing_strategy", "sentence_aware_splitting");
        }

        List<Document> enhancedDocuments = metadataEnhancer.enhanceDocumentsMetadata(documents, documentType, textMetadata);

        log.debug("文本文档元数据增强完成");
        return enhancedDocuments;
    }

    @Override
    public List<Document> validateQuality(List<Document> documents) {
        if (documents == null || documents.isEmpty()) {
            return new ArrayList<>();
        }

        log.debug("开始文本文档质量验证，文档数: {}", documents.size());

        List<Document> validDocuments = new ArrayList<>();
        int filteredCount = 0;

        for (Document document : documents) {
            // 检查文档内容质量
            if (documentCleaningService.isValidContent(document.getText())) {
                // 检查质量分数
                Object qualityScore = document.getMetadata().get("overall_quality_score");
                if (qualityScore instanceof Double && (Double) qualityScore >= processingConfig.getQuality().getMinOverallQualityScore()) {
                    validDocuments.add(document);
                } else {
                    filteredCount++;
                    log.debug("过滤低质量文本文档块，质量分数: {}", qualityScore);
                }
            } else {
                filteredCount++;
                log.debug("过滤无效文本文档块，内容长度: {}", document.getText().length());
            }
        }

        log.debug("文本文档质量验证完成，有效文档: {}, 过滤文档: {}", validDocuments.size(), filteredCount);
        return validDocuments;
    }

    @Override
    public List<String> getSupportedFileTypes() {
        return List.of("txt", "md", "markdown");
    }

    @Override
    public String getProcessorName() {
        return "智能文本文档处理器";
    }

    @Override
    public String getProcessorVersion() {
        return "2.0.0-smart";
    }

    @Override
    public boolean supportsSmartProcessing() {
        return true;
    }

    @Override
    public String getConfigurationInfo() {
        return String.format("SmartTextProcessor[chunk_size=%d, overlap=%.2f, cleaning=%s, metadata=%s]",
            processingConfig.getChunkSizeForType("txt"),
            processingConfig.getOverlapRatioForType("txt"),
            processingConfig.getCleaning().isEnabled(),
            processingConfig.getMetadata().isEnabled());
    }

    /**
     * 根据元数据确定文档类型
     */
    private String determineDocumentType(Map<String, Object> metadata) {
        // 从文件名或其他元数据中推断文档类型
        Object fileName = metadata.get("source");
        if (fileName != null) {
            String fileNameStr = fileName.toString().toLowerCase();
            if (fileNameStr.endsWith(".md") || fileNameStr.endsWith(".markdown")) {
                return "md";
            }
        }

        // 默认为txt
        return "txt";
    }

}
