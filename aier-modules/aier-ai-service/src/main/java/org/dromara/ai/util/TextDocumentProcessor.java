/*
 * Copyright 2025 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.dromara.ai.util;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.ai.document.Document;
import org.springframework.ai.reader.TextReader;
import org.springframework.ai.transformer.splitter.TokenTextSplitter;
import org.springframework.core.io.InputStreamResource;
import org.springframework.stereotype.Component;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 文本文档处理器
 * 处理纯文本文件和Markdown文件
 *
 * <AUTHOR> <PERSON>uppy
 */
@Slf4j
@Component
public class TextDocumentProcessor implements DocumentProcessor {

    @Override
    public boolean supports(String fileType) {
        return "txt".equalsIgnoreCase(fileType) || 
               "md".equalsIgnoreCase(fileType) || 
               "markdown".equalsIgnoreCase(fileType);
    }

    @Override
    public List<Document> process(InputStream inputStream, Map<String, Object> metadata) throws Exception {
        log.info("开始处理文本文档，元数据: {}", metadata);

        try {
            // 使用Spring AI的TextReader读取文本
            TextReader textReader = new TextReader(new InputStreamResource(inputStream));
            textReader.getCustomMetadata().putAll(metadata);
            
            // 读取文档
            List<Document> documents = textReader.get();
            
            if (documents.isEmpty()) {
                log.warn("文本文档内容为空");
                return List.of();
            }

            // 验证文档内容
            for (Document document : documents) {
                if (StrUtil.isBlank(document.getText())) {
                    log.warn("发现空白文档内容");
                    continue;
                }
                // 确保元数据正确设置
                document.getMetadata().putAll(metadata);
            }

            // 使用文本分割器进行分块
            // 配置TokenTextSplitter参数以确保生成的文档块不超过200字符限制
            // defaultChunkSize: 30 tokens(约120字符), minChunkSizeChars: 50字符, minChunkLengthToEmbed: 5, maxNumChunks: 10000, keepSeparator: true
            TokenTextSplitter textSplitter = new TokenTextSplitter(30, 50, 5, 10000, true);
            List<Document> splitDocuments = textSplitter.apply(documents);

            // 对超过200字符的文档块进行二次分割，确保严格控制长度
            List<Document> finalDocuments = ensureMaxLength(splitDocuments, 200);

            log.info("文本文档处理完成，原始文档数: {}, 分块后文档数: {}, 最终文档数: {}",
                documents.size(), splitDocuments.size(), finalDocuments.size());
            return finalDocuments;

        } catch (Exception e) {
            log.error("文本文档处理失败", e);
            throw new RuntimeException("文本文档处理失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<String> getSupportedFileTypes() {
        return List.of("txt", "md", "markdown");
    }

    @Override
    public String getProcessorName() {
        return "文本文档处理器";
    }

    /**
     * 确保文档块长度不超过指定的最大长度
     * 对超长的文档块进行二次分割
     *
     * @param documents 原始文档列表
     * @param maxLength 最大长度限制
     * @return 长度符合要求的文档列表
     */
    private List<Document> ensureMaxLength(List<Document> documents, int maxLength) {
        List<Document> result = new ArrayList<>();

        for (Document doc : documents) {
            String content = doc.getText();
            if (content == null || content.length() <= maxLength) {
                result.add(doc);
                continue;
            }

            // 对超长文档进行分割
            log.warn("文档内容长度 {} 超过限制 {}，进行二次分割", content.length(), maxLength);
            List<Document> splitDocs = splitLongDocument(doc, maxLength);
            result.addAll(splitDocs);
        }

        return result;
    }

    /**
     * 分割超长文档
     * 在保持语义完整性的前提下，将文档分割为符合长度要求的块
     *
     * @param document  原始文档
     * @param maxLength 最大长度限制
     * @return 分割后的文档列表
     */
    private List<Document> splitLongDocument(Document document, int maxLength) {
        List<Document> result = new ArrayList<>();
        String content = document.getText();

        // 尝试按句子分割（以句号、问号、感叹号为分隔符）
        String[] sentences = content.split("[。！？.!?]");
        StringBuilder currentChunk = new StringBuilder();

        for (String sentence : sentences) {
            sentence = sentence.trim();
            if (sentence.isEmpty()) continue;

            // 如果单个句子就超过长度限制，强制分割
            if (sentence.length() > maxLength) {
                // 先保存当前块
                if (currentChunk.length() > 0) {
                    result.add(createDocumentChunk(document, currentChunk.toString()));
                    currentChunk.setLength(0);
                }

                // 强制分割超长句子
                for (int i = 0; i < sentence.length(); i += maxLength) {
                    int end = Math.min(i + maxLength, sentence.length());
                    String chunk = sentence.substring(i, end);
                    result.add(createDocumentChunk(document, chunk));
                }
                continue;
            }

            // 检查添加当前句子是否会超过长度限制
            if (currentChunk.length() + sentence.length() + 1 > maxLength) {
                // 保存当前块
                if (currentChunk.length() > 0) {
                    result.add(createDocumentChunk(document, currentChunk.toString()));
                    currentChunk.setLength(0);
                }
            }

            // 添加当前句子
            if (currentChunk.length() > 0) {
                currentChunk.append("。");
            }
            currentChunk.append(sentence);
        }

        // 保存最后一个块
        if (currentChunk.length() > 0) {
            result.add(createDocumentChunk(document, currentChunk.toString()));
        }

        return result;
    }

    /**
     * 创建文档块
     * 复制原始文档的元数据到新的文档块
     *
     * @param originalDoc 原始文档
     * @param content     文档内容
     * @return 新的文档块
     */
    private Document createDocumentChunk(Document originalDoc, String content) {
        Document chunk = new Document(content);
        chunk.getMetadata().putAll(originalDoc.getMetadata());
        return chunk;
    }

}
